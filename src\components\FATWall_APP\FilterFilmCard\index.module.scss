.card_container {
  width: 100%;
  max-width: 110px;
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
  user-select: none;
  cursor: pointer;
}

.card_content {
  width: 100%;
  position: relative;
  height: 150px;

  .card_core {
    position: absolute;
    bottom: 10px;
    right: 5px;
    font-family: MiSans W;
    font-weight: 500;
    font-size: 17.29px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #fff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }

  .card_isLike {
    position: absolute;
    bottom: 0;
    left: 5px;
    img {
      height: 26px !important;
    }
  }
}

.card_footer {
  display: flex;
  flex-direction: column;
  padding-top: 8px;
}

.card_text {
  width: 100%;
  font-family: MiSans W;
  font-weight: 500;
  font-size: 17.29px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--text-color);
  margin: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card_text_drama {
  font-size: 14px;
  color: #fff;
}

.card_info {
  display: flex;
  align-items: center;
  gap: 5px;

  // justify-content: start;
  .episodes {
    font-family: MiSans;
    font-weight: 300;
    font-size: 12.97px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #aaa;
  }

  .card_subtitle {
    font-family: MiSans;
    font-weight: 300;
    font-size: 12.97px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #aaa;
  }

  .card_subtitle_drama {
    color: #aaa;
    // font-size: 12.97px;
  }
}
