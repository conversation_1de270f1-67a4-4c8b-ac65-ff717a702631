.synchronizationContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color, #ffffff);
}

.fixedHeader {
  flex-shrink: 0;
  background-color: var(--background-color, #ffffff);
}

.title {
  font-size: 24px;
  padding: 0 16px;
  color: var(--text-color, #000000);
  margin-bottom: 12px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--background-color, #ffffff);
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 8px;
  position: relative;
  justify-content: space-between;

  &::-webkit-scrollbar {
    display: none;
  }

  .breadcrumbItem {
    color: rgba(255, 178, 29, 1);
    background-color: #fff4dd;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    flex-shrink: 0;

    &.active {
      background-color: rgba(255, 178, 29, 0.2);
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--secondary-text-color, #8c93b0);
    font-size: 12px;
    flex-shrink: 0;
  }

  .selectAllCheckbox {
    flex-shrink: 0;
    margin-left: auto;
  }
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; // 为底部按钮留出空间
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--secondary-text-color, #8c93b0);

  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  padding: 0 16px;

  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .fileInfo {
      flex: 1;
      overflow: hidden;

      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color, #000000);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .heartIcon {
          margin-left: 8px;
          color: #ff5c5c;
          font-size: 14px;
          flex-shrink: 0;
        }
      }

      .fileDetails {
        font-size: 12px;
        color: var(--secondary-text-color, #8c93b0);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .checkboxContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 12px;
    }
  }

  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8c93b0);
    font-size: 14px;
  }
}

.footerButtons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 16px;
  background-color: #fff;
  .locationButton {
    border-radius: 30px;
    background-color: #fff;
    border-color: #aeaeae;
    color: #898989;
    margin-right: 16px;
    font-size: 12px;
    height: 50px;
    padding: 5px 16px;
    text-align: left;
  }
  .downloadButton {
    background-color: #32bac0;
    border-radius: 30px;
    border: none;
    max-width: 168px;
  }
  :global {
    .adm-button:active::before {
      opacity: 0;
    }
    .adm-button-disabled {
      background-color: #999;
    }
  }

  .vipTip {
    position: absolute;
    top: 0;
    left: 45%;
    transform: translateX(-50%);
    background-color: #402c00;
    color: #e2ae1e;
    padding: 2px 5px;
    border-radius: 5px;
    font-size: 12px;
    border: 0.1px solid #e2ae1e;
    z-index: 10;
  }
}
.pathText {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
