import { useRef, useEffect, useState } from "react";
import { But<PERSON>, List, Image, Toast } from "antd-mobile";
import { useHistory, useLocation } from "react-router-dom";
import PopoverSelector from "@/components/PopoverSelector";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import filter from "@/Resources/camMgmtImg/filter.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import filterDark from "@/Resources/camMgmtImg/filter-dark.png";
import close from "@/Resources/camMgmtImg/close.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import cameraIcon from "@/Resources/camMgmtImg/camera-icon.png";
import cameraIconDark from "@/Resources/camMgmtImg/camera-icon-dark.png";
import successCof from "@/Resources/camMgmtImg/success-cof.png";
import successCofDark from "@/Resources/camMgmtImg/success-cof-dark.png";
import errorIcon from "@/Resources/camMgmtImg/search-fail.png";
import errorIconDark from "@/Resources/camMgmtImg/search-fail-dark.png";
import NavigatorBar from "@/components/NavBar";
import CameraStatusPage from "@/components/CameraStatusPage";
import { useRecordingSettings, getRecordingOptions } from "./recordingSettings";
import { useTheme } from "@/utils/themeDetector";
import { setupRecordCamera, listRecordCamera } from "@/api/ipc";
import { useRequest } from "ahooks";

const LocalRecording = () => {
  const history = useHistory();
  const { isDarkMode } = useTheme();
  const location = useLocation<{ selectedDevices: any }>();

  // 新增配置状态管理
  const [configStatus, setConfigStatus] = useState<
    "settings" | "configuring" | "success" | "error"
  >("settings");

  // 获取从上一页传递的选中设备数据
  const selectedDevices = location.state?.selectedDevices || [];

  // 使用共享的录制设置逻辑，并指定为App模式
  const {
    settings,
    popoverState,
    updatePopoverVisibility,
    handleDurationChange,
    handleModeChange,
    handleQualityChange,
    calculateEstimatedSpace,
  } = useRecordingSettings(true);

  // 获取选项数据
  const { durationOptions, modeOptions, qualityOptions } =
    getRecordingOptions();

  // 引用元素
  const durationTriggerRef = useRef<HTMLDivElement>(null);
  const modeTriggerRef = useRef<HTMLDivElement>(null);
  const qualityTriggerRef = useRef<HTMLDivElement>(null);

  // 首次加载时计算预估存储空间
  useEffect(() => {
    calculateEstimatedSpace();
  }, []);

  // 设置API请求
  const { loading: submitLoading, run: runSetupCamera } = useRequest(
    setupRecordCamera,
    {
      manual: true,
      debounceWait: 300,
      onSuccess: (result) => {
        console.log("摄像机录制设置成功:", result);
        setConfigStatus("success");
      },
      onError: (error) => {
        console.error("摄像机录制设置失败:", error);
        Toast.show({
          content: "设置失败，请重试",
          position: "bottom",
        });
        setConfigStatus("error");
      },
    }
  );

  // 添加刷新摄像头列表的请求
  const { run: refreshCameraList } = useRequest(
    () => listRecordCamera({ did: [] }),
    {
      manual: true,
    }
  );

  // 将时长转换为天数
  const getDurationDays = (durationText: string): number => {
    const days = parseInt(durationText.replace("天", ""), 10);
    return isNaN(days) ? 30 : days; // 默认30天
  };

  // 提取存储大小数字
  const getStorageSizeGB = (sizeText: string): number => {
    const size = parseFloat(sizeText.replace("GB", ""));
    return isNaN(size) ? 50 : size; // 默认50GB
  };

  const handleNext = () => {
    // 获取选中设备的ID数组
    const cameraIds = selectedDevices.map((device: any) => device.id);

    if (cameraIds.length === 0) {
      Toast.show({
        content: "未选择任何设备",
        position: "bottom",
      });
      return;
    }

    // 构建请求参数
    const params = {
      camera: cameraIds,
      config: {
        path: settings.storagePath,
        retention_period: getDurationDays(settings.duration),
        record_mode: settings.mode === "连续录制" ? "continuous" : "event",
        record_resolution: settings.quality,
        space_limit: getStorageSizeGB(settings.estimatedSpace),
      },
    };

    console.log("调用摄像机录制设置API，参数:", params);

    // 切换到配置中状态
    setConfigStatus("configuring");

    // 给一点时间让配置页面先加载出来
    setTimeout(() => {
      // 然后调用设置API
      runSetupCamera(params);
    }, 300);
  };

  const CustomArrow = () => (
    <img
      alt=""
      src={isDarkMode ? filterDark : filter}
      className={styles.customArrow}
    />
  );

  const showSuccessToast = () => {
    Toast.show({
      content: "设置成功",
      position: "bottom",
    });
  };

  // 包装处理函数，添加成功提示
  const onDurationChange = (value: string) => {
    handleDurationChange(value);
    showSuccessToast();
  };

  const onModeChange = (value: string) => {
    handleModeChange(value);
    showSuccessToast();
  };

  const onQualityChange = (value: string) => {
    handleQualityChange(value);
    showSuccessToast();
  };

  // 处理配置页面的返回操作
  const handleGoTo = () => {
    // 直接调用API刷新摄像头列表
    refreshCameraList();

    // 跳转到首页
    history.push("/cameraManagement_app");
  };

  // 处理配置失败时的重试操作
  const handleRetry = () => {
    setConfigStatus("settings");
  };

  // 配置页面的返回按钮
  const leftHeaderContent = (
    <Image
      className={styles.close}
      src={isDarkMode ? closeDark : close}
      onClick={() => {
        history.push("/cameraManagement_app");
      }}
    />
  );

  // 如果在配置状态，显示配置页面
  if (configStatus !== "settings") {
    // 配置中状态
    if (configStatus === "configuring") {
      const configuringIcon = (
        <Image
          src={isDarkMode ? cameraIconDark : cameraIcon}
          className={`${styles.loadingCircle} ${styles.rotating}`}
        />
      );

      return (
        <CameraStatusPage
          className={styles.container}
          leftHeaderContent={leftHeaderContent}
          title="配置中"
          icon={configuringIcon}
          mainMessage="摄像机配置中，请稍等"
          subMessage="大概需要花费10秒"
          showButton={false}
        />
      );
    }

    // 失败状态
    if (configStatus === "error") {
      const failIcon = (
        <Image
          src={isDarkMode ? errorIconDark : errorIcon}
          className={styles.errorIcon}
        />
      );

      return (
        <CameraStatusPage
          className={styles.container}
          leftHeaderContent={leftHeaderContent}
          title="配置失败"
          icon={failIcon}
          mainMessage="配置失败，请重试"
          buttonText="重新设置"
          onButtonClick={handleRetry}
        />
      );
    }

    // 成功状态
    const successIcon = (
      <Image
        src={isDarkMode ? successCofDark : successCof}
        className={styles.successIcon}
      />
    );

    return (
      <CameraStatusPage
        className={styles.container}
        leftHeaderContent={leftHeaderContent}
        title="配置成功"
        icon={successIcon}
        mainMessage="配置完成"
        buttonText="去看看"
        onButtonClick={handleGoTo}
      />
    );
  }

  // 显示设置表单
  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>摄像机本地录制</div>

      <div className={styles.settingsCard}>
        <div className={styles.cardTitle}>视频存储设置</div>

        <List className={styles.settingsList}>
          <List.Item className={styles.settingItem} arrow={false}>
            <div className={styles.settingContent}>
              <div className={styles.settingLabel}>存储目录</div>
              <div className={styles.settingValue}>{settings.storagePath}</div>
            </div>
          </List.Item>

          <div ref={durationTriggerRef}>
            <PopoverSelector
              visible={popoverState.durationPopoverVisible}
              onVisibleChange={(visible) =>
                updatePopoverVisibility("durationPopoverVisible", visible)
              }
              value={settings.duration}
              options={durationOptions}
              onChange={onDurationChange}
            >
              <List.Item className={styles.settingItem} arrow={<CustomArrow />}>
                <div className={styles.settingContent}>
                  <div className={styles.settingLabel}>录像保存时长</div>
                  <div className={styles.settingValue}>{settings.duration}</div>
                </div>
              </List.Item>
            </PopoverSelector>
          </div>

          <div ref={modeTriggerRef}>
            <PopoverSelector
              visible={popoverState.modePopoverVisible}
              onVisibleChange={(visible) =>
                updatePopoverVisibility("modePopoverVisible", visible)
              }
              value={settings.mode}
              options={modeOptions}
              onChange={onModeChange}
            >
              <List.Item className={styles.settingItem} arrow={<CustomArrow />}>
                <div className={styles.settingContent}>
                  <div className={styles.settingLabel}>录制模式</div>
                  <div className={styles.settingValue}>{settings.mode}</div>
                </div>
              </List.Item>
            </PopoverSelector>
          </div>

          <div ref={qualityTriggerRef}>
            <PopoverSelector
              visible={popoverState.qualityPopoverVisible || false}
              onVisibleChange={(visible) =>
                updatePopoverVisibility("qualityPopoverVisible", visible)
              }
              value={settings.quality || ""}
              options={qualityOptions}
              onChange={onQualityChange}
            >
              <List.Item className={styles.settingItem} arrow={<CustomArrow />}>
                <div className={styles.settingContent}>
                  <div className={styles.settingLabel}>录制清晰度</div>
                  <div className={styles.settingValue}>{settings.quality}</div>
                </div>
              </List.Item>
            </PopoverSelector>
          </div>

          {settings.mode === "连续录制" && (
            <List.Item className={styles.settingItem}>
              <div className={styles.settingContent}>
                <div className={styles.settingLabel}>预计占用存储空间</div>
                <div className={styles.settingValue}>
                  {settings.estimatedSpace}
                </div>
              </div>
            </List.Item>
          )}
        </List>
      </div>

      <div className={styles.note}>
        Xiaomi智能存储将为每个摄像头建立单独文件夹，更多设置可前往Xiaomi智能存储设备管理页完成。
      </div>

      <div className={styles.footer}>
        <Button
          block
          color="primary"
          className={styles.nextButton}
          onClick={handleNext}
          loading={submitLoading}
        >
          下一步
        </Button>
      </div>
    </div>
  );
};

export default LocalRecording;
