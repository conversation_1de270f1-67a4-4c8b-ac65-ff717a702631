import React, { useState, useRef, useEffect, useCallback } from "react";
import styles from "./index.module.scss";

import FilmCard from "../../../../../components/FATWall_APP/FilmCard";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import edit from "@/Resources/camMgmtImg/edit.png";
import editDark from "@/Resources/camMgmtImg/edit-dark.png";
import finish from "@/Resources/camMgmtImg/finish.png";
import finishDark from "@/Resources/camMgmtImg/finish-dark.png";
import deleted from "@/Resources/camMgmtImg/delete.png";
import deleteDark from "@/Resources/camMgmtImg/deletes-dark.png";
import close from "@/Resources/camMgmtImg/close.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import { useHistory } from "react-router-dom";
import { Checkbox, Toast } from "antd-mobile";
import { modalShow } from "@/components/List";
import { PreloadImage } from "@/components/Image";
import { useRequest } from 'ahooks';
import { getRecentlyWatched, mediaClearWatched, RecentlyPlayedFileInfo } from "@/api/fatWall";
import { px2rem } from "@/utils/setRootFontSize";

// 格式化时间的工具函数
export const formatTimeAgo = (timestamp: number): string => {
  if (!timestamp) return '';
  
  // 计算当前时间与传入时间戳的差值（秒）
  const now = Math.floor(Date.now() / 1000);
  const seconds = now - timestamp;
  
  const minutes = seconds / 60;
  const hours = minutes / 60;
  const days = hours / 24;
  
  if (minutes < 1) {
    return `${Math.floor(seconds)}秒`;
  } else if (minutes < 60) {
    return `${Math.floor(minutes)}分钟`;
  } else if (hours < 24) {
    return `${Math.floor(hours)}小时`;
  } else if (days < 7) {
    return `${Math.floor(days)}天`;
  } else if (days < 30) {
    return `${Math.floor(days / 7)}周`;
  } else if (days < 365) {
    return `${Math.floor(days / 30)}个月`;
  } else {
    return `${Math.floor(days / 365)}年`;
  }
};

export default function RecentlyPlay() {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const [isEditMode, setIsEditMode] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [initialLoaded, setInitialLoaded] = useState(false);

  // 最近播放列表
  const [recentlyPlayList, setRecentlyPlayList] = useState<(RecentlyPlayedFileInfo & { selected?: boolean })[]>([]);

  // 请求最近播放接口
  const { run: fetchRecentlyWatched } = useRequest(
    (params) => getRecentlyWatched(params), 
    {
      manual: true,
      onSuccess: (res) => {
        const newData = (res.data as any)?.files.map((item: RecentlyPlayedFileInfo) => ({ ...item, selected: false }));
        if (offset === 0) {
          setRecentlyPlayList(newData);
          setInitialLoaded(true);
          setHasMore(newData.length === 50);
        } else {
          setRecentlyPlayList(prev => [...prev, ...newData]);
          setHasMore(newData.length === 50);
        }
        setLoading(false);
      },
    }
  );

  useEffect(() => {
    fetchRecentlyWatched({ offset: 0, limit: 50 });
  }, []);

  const loadMore = useCallback(() => {
    if (!hasMore || loading || isEditMode) return;
    setLoading(true);
    const newOffset = offset + 50;
    setOffset(newOffset);
    fetchRecentlyWatched({ offset: newOffset, limit: 50 });
  }, [offset, hasMore, loading, isEditMode, fetchRecentlyWatched]);

  const { run: runMediaClearWatched } = useRequest(
    mediaClearWatched,
    {
      manual: true,
      onSuccess: () => {
        Toast.show({
          content: "删除成功",
          duration: 2000,
          position: "bottom",
        });
      },
      onError: () => {
        Toast.show({
          content: "删除失败，请重试",
          duration: 2000,
          position: "bottom",
        });
      },
    }
  );

  const toggleEditMode = () => {
    if (isEditMode) {
      setRecentlyPlayList(
        recentlyPlayList.map((item) => ({
          ...item,
          selected: false,
        }))
      );
    }
    setIsEditMode(!isEditMode);
  };

  const handleBack = () => {
    if (isEditMode) {
      setIsEditMode(false);
      setRecentlyPlayList(
        recentlyPlayList.map((item) => ({
          ...item,
          selected: false,
        }))
      );
    } else {
      history.goBack();
    }
  };

  const toggleItemSelection = (id: string) => {
    if (!isEditMode) return;

    setRecentlyPlayList(
      recentlyPlayList.map((item) =>
        item.file_id === Number(id) ? { ...item, selected: !item.selected } : item
      )
    );
  };

  const deleteSelectedItems = () => {
    const selectedItems = recentlyPlayList.filter((item) => item.selected);
    const selectedIds = selectedItems.map(item => item.file_id);

    modalShow(
      "确定删除观看记录？",
      <div className={styles.modalConfirmText}>{/* 确定删除观看记录？ */}</div>,
      (m) => {
        m.destroy();
        runMediaClearWatched({ file_ids: selectedIds });
        const newList = recentlyPlayList.filter((item) => !item.selected);
        setRecentlyPlayList(newList);
        if (newList.length === 0) {
          history.push("/filmAndTelevisionWall_app/recently");
        }
        Toast.show({
          content: "删除成功",
          duration: 2000,
          position: "bottom",
        });
      },
      () => {}, // onCancel
      false, // onlyShow
      {
        okBtnText: "删除",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom",
      }
    );
  };

  const hasSelectedItems = recentlyPlayList.some((item) => item.selected);

  const arrowIcon = isDarkMode ? arrowLeftDark : arrowLeft;

  const closeIcon = isDarkMode ? closeDark : close;

  const finishIcon = isDarkMode ? finishDark : finish;

  const editIcon = isDarkMode ? editDark : edit;

  // 添加列表容器的引用
  const filmsContainerRef = useRef<HTMLDivElement>(null);
  
  // 监听编辑模式变化，重置滚动位置
  useEffect(() => {
    if (filmsContainerRef.current && !isEditMode) {
      filmsContainerRef.current.scrollTop = 0;
    }
  }, [isEditMode]);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      if (!filmsContainerRef.current) return;
      
      const { scrollTop, scrollHeight, clientHeight } = filmsContainerRef.current;
      // 只有初始50条数据加载完成，并且滚动到底部时才加载更多
      if (initialLoaded && scrollHeight - scrollTop - clientHeight < 20 && hasMore && !loading && !isEditMode) {
        loadMore();
      }
    };

    const container = filmsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [hasMore, loading, isEditMode, loadMore, initialLoaded]);

  return (
    <div className={styles.container}>
      <NavigatorBar
        backIcon={isEditMode ? closeIcon : arrowIcon}
        right={
          recentlyPlayList.length > 0 && (
            <PreloadImage
              src={isEditMode ? finishIcon : editIcon}
              alt="edit"
              onClick={toggleEditMode}
            />
          )
        }
        onBack={handleBack}
      />
      <div className={styles.title}>{isEditMode ? "编辑" : "最近播放"}</div>
      
      <div className={styles.filmsContainer} ref={filmsContainerRef}>
          {recentlyPlayList.length > 0 ? (
            recentlyPlayList.map((item) => (
              <div key={item.file_id} className={styles.filmItem}>
                <FilmCard
                  poster={item.poster ? item.poster.length > 0 ? item.poster.length > 1 ? item.poster[1] : item.poster[0] : '' : ''}
                  title={item.media_name || ''}
                  progress={item?.last_seen_percent}
                  time={formatTimeAgo(item.last_seen_time)}
                  type="play"
                  layout="horizontal"
                  options={{
                    style: { width: px2rem("121px"), height: px2rem("78px") },
                    callback: () =>
                      !isEditMode && console.log(`Clicked on ${item.media_name}`),
                  }}
                />
                {isEditMode && (
                  <Checkbox
                    checked={item.selected}
                    onChange={() => toggleItemSelection(item.file_id.toString())}
                    className={styles.antdCheckbox}
                  />
                )}
              </div>
            ))
          ) : (
            <div className={styles.noDataTip}>暂无数据</div>
          )}
        </div>
      
      {isEditMode && (
        <div className={styles.deleteContainer}>
          <div>
            <PreloadImage
              src={isDarkMode ? deleteDark : deleted}
              className={`${styles.deleteButton} ${
                !hasSelectedItems ? styles.disabled : ""
              }`}
              onClick={hasSelectedItems ? deleteSelectedItems : undefined}
            />
          </div>
          <div className={styles.deletdRecord}>删除记录</div>
        </div>
      )}
    </div>
  );
}
