import React, { useState, useEffect, useCallback } from "react";
import { Button, Input, Modal, message } from "antd";
import { CloseOutlined, EditOutlined, CheckOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { useHistory } from "react-router-dom";
import avatar from "@/Resources/camMgmtImg/avatar.png";
import { useRequest } from "ahooks";
import {
  setFacialInfo,
  getFacialPhoto,
  delFacialPhoto,
  delFacialVideo,
  getFacialVideo,
  getFacialInfo,
} from "@/api/ipc";
import {
  PhotoItem,
  VideoItem,
  togglePhotoSelection as togglePhoto,
  resetEditState as resetEdit,
} from "@/pages/IPC/IPC_APP/FaceRecognition/FaceDetail/faceRecognition";
import nullData from "@/Resources/camMgmtImg/null-page.png";
import nullDataDark from "@/Resources/camMgmtImg/null-page-dark.png";
import { useTheme } from "@/utils/themeDetector";
import LookBackMovieList from "../../EventLookBack/LookBackMovieList";
import { modalShow } from "@/components/List";

import { eventLookBackData, eventLookBackDataType } from "../../EventLookBack";
import { format } from "date-fns";
import { eventDefinition } from "@/components/CameraPlayer/constants";
import { PreloadImage } from "@/components/Image";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";

interface FaceDetailProps {
  visible?: boolean;
  faceId?: string;
  faceName?: string;
  onClose?: (needRefresh?: boolean) => void;
  profilePic?: string; // 添加头像参数
}

const FaceDetail: React.FC<FaceDetailProps> = ({
  visible = true,
  faceId,
  faceName: initialFaceName = "备注名称",
  onClose,
  profilePic, // 接收传入的头像
}) => {
  const [activeTab, setActiveTab] = useState("最近视频");
  const [faceName, setFaceName] = useState(initialFaceName);
  const [displayName, setDisplayName] = useState(initialFaceName);
  const [isEditingName, setIsEditingName] = useState(false);
  const [hasVideoData, setHasVideoData] = useState(true);
  const [isEditingPhotos, setIsEditingPhotos] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<number[]>([]);
  const [deleteToastVisible, setDeleteToastVisible] = useState(false);
  const [isDeleteOperation, setIsDeleteOperation] = useState(false);
  const [isSaveButtonClicked, setIsSaveButtonClicked] = useState(false);
  // const [deletedPhotoPosition, setDeletedPhotoPosition] = useState<{
  //   top: number;
  //   left: number;
  // }>({ top: 0, left: 0 });
  // const [deleteUserModalVisible, setDeleteUserModalVisible] = useState(false);
  const [eventData, setEventData] = useState<eventLookBackData[]>([]);
  const [curData, setCurData] = useState<eventLookBackDataType | undefined>();
  const [modalIsShow, setModalIsShow] = useState<boolean>(false);

  const history = useHistory();
  const { isDarkMode } = useTheme();

  useEffect(() => {
    if (visible) {
      setActiveTab("最近视频");
      setFaceName(initialFaceName);
      setDisplayName(initialFaceName);
      setIsEditingName(false);
      setIsEditingPhotos(false);
      setSelectedPhotos([]);
      setAlbumPhotos(
        albumPhotos.map((photo) => ({ ...photo, selected: false }))
      );
      setCapturePhotos(
        capturePhotos.map((photo) => ({ ...photo, selected: false }))
      );
      setDeleteToastVisible(false);

      // 初始化时加载所有数据（照片和视频），与APP端保持一致
      if (faceId) {
        refreshPhotos();
        refreshVideos(); // 同时加载视频数据
      }
    }
  }, [visible, initialFaceName]);

  // 获取人脸照片 - 与APP端保持一致
  const { run: refreshPhotos } = useRequest(
    () => getFacialPhoto({ uuid: faceId as string }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 直接使用返回的照片路径数组
          const photos = res.data.photo.map((url: string, index: number) => ({
            id: `photo-${index}`,
            src: url,
            selected: false,
          }));

          // 更新照片列表
          setAlbumPhotos(photos);
        } else {
          message.error(res?.result);
        }
      },
      onError: (error) => {
        console.error("获取人脸照片失败:", error);
      },
    }
  );

  // 获取视频列表 - 与APP端保持一致
  const { run: refreshVideos } = useRequest(
    () =>
      getFacialVideo({
        page: {
          size: 20,
          token: "",
        },
        uuid: faceId as string,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 处理视频数据，添加日期和是否今天的标记
          let obj: { [key: string]: eventLookBackDataType[] } = {};
          const videos = res.data.videos.map((video: any, index: number) => {
            const date = new Date(parseInt(video.time));
            const today = new Date();
            const isToday = date.toDateString() === today.toDateString();

            return {
              ...video,
              id: `video-${index}`,
              camera_lens: video.camera_lens || "",
              event_name: video.event_name || "move",
              face_file: video.face_file,
              cover_file: video.cover_file,
              date: date.toLocaleDateString(),
              isToday,
              subText: `识别到${displayName}`,
            };
          });

          setVideoData(videos);
          setHasVideoData(videos.length > 0);
          setEventData(
            Object.keys(obj).map((key) => {
              return { date: key, data: obj[key] };
            })
          );
        } else {
          message.error(res?.result);
        }
      },
      onError: (error) => {
        console.error("获取视频列表失败:", error);
        message.error("获取视频列表失败，请重试");
      },
    }
  );

  // 模拟相册照片数据
  const [albumPhotos, setAlbumPhotos] = useState<PhotoItem[]>([]);

  // 模拟摄像机抓拍数据
  const [capturePhotos, setCapturePhotos] = useState<PhotoItem[]>([
    { id: 9, src: avatar, selected: false },
    { id: 10, src: avatar, selected: false },
    { id: 11, src: avatar, selected: false },
  ]);

  // 模拟视频数据
  const [videoData, setVideoData] = useState<VideoItem[]>([]);

  // 获取人脸列表信息 - 用于在修改名称后刷新列表
  const { run: refreshFaceList } = useRequest(getFacialInfo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        console.log("人脸列表刷新成功");
        // 这里不需要处理返回数据，只需要触发API调用即可
        // 父组件会自行处理列表更新
      } else {
        message.error(res?.result || "刷新人脸列表失败");
      }
    },
    onError: (error) => {
      console.error("刷新人脸列表失败:", error);
      message.error("刷新人脸列表失败");
    },
  });

  // 添加handleClose函数处理关闭
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      history.goBack();
    }
  };

  // 设置人脸信息API - 与APP端保持一致
  const { run: saveFaceInfo } = useRequest(setFacialInfo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        message.success("保存成功");

        if (isDeleteOperation) {
          // 关闭弹窗并通知父组件需要刷新
          if (onClose) {
            onClose(true); // 传递true表示需要刷新列表
          } else {
            history.goBack();
          }
          // 重置操作类型标志
          setIsDeleteOperation(false);
        } else {
          // 处理不同的操作类型
          if (isSaveButtonClicked) {
            // 如果是点击保存按钮，刷新人脸列表并关闭弹窗
            refreshFaceList();
            setIsSaveButtonClicked(false);

            // 关闭弹窗并通知父组件需要刷新
            if (onClose) {
              onClose(true); // 传递true表示需要刷新列表
            } else {
              history.goBack();
            }
          } else {
            // 如果是双击修改名称，只刷新视频列表，不关闭弹窗
            refreshVideos();
          }
        }
      } else {
        message.error(res?.result);
      }
    },
    onError: (error) => {
      console.error("保存人脸信息失败:", error);
      message.error("操作失败，请重试");
    },
  });

  // 删除人脸照片
  const { run: runDeletePhoto } = useRequest(delFacialPhoto, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        message.success("删除成功");

        // 删除成功后直接重新获取照片列表
        refreshPhotos();

        // 重置编辑状态
        exitEditMode();
      } else {
        message.error(res?.result || "删除失败");
      }
    },
    onError: (error) => {
      console.error("删除照片失败:", error);
      message.error("删除失败，请重试");
    },
  });

  // 删除视频
  const { run: runDeleteVideo } = useRequest(delFacialVideo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        message.success("删除成功");
        // 删除成功后重新获取视频列表
        refreshVideos();
      } else {
        message.error(res?.result || "删除失败");
      }
    },
    onError: (error) => {
      console.error("删除视频失败:", error);
      message.error("删除失败，请重试");
    },
  });

  // 处理删除标记
  const handleDeleteMark = () => {
    modalShow(
      "",
      <div className={styles.deleteConfirmText}>删除后，该任务将以陌生人显示录像保留，确认删除？</div>,
      (m) => {
        m.destroy();
        // 调用删除标记API
        if (faceId) {
          // 设置操作类型为删除操作
          setIsDeleteOperation(true);
          saveFaceInfo({
            uuid: faceId,
            profile_pic: profilePic || '',
            name: '',
            delete: false,
          });
        } else {
          message.error("未找到用户ID，无法删除");
        }
      },
      () => {}, // onCancel
      false, // onlyShow
      {
        okBtnText: "删除",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "center",
      }
    );
  };

  // 处理彻底删除
  const handlePermanentDelete = () => {
    modalShow(
      "",
      <div className={styles.deleteConfirmText}>将删除该人物所有识别信息及相关视频，确认删除？</div>,
      (m) => {
        m.destroy();
        // 调用彻底删除API
        if (faceId) {
          // 设置操作类型为删除操作
          setIsDeleteOperation(true);
          saveFaceInfo({
            uuid: faceId,
            profile_pic: profilePic || '',
            name: faceName || '',
            delete: true,
          });
        } else {
          message.error("未找到用户ID，无法删除");
        }
      },
      () => {}, // onCancel
      false, // onlyShow
      {
        okBtnText: "删除",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "center",
      }
    );
  };

  // 处理保存
  const handleSave = () => {
    // 调用保存用户API
    if (faceId) {
      setIsSaveButtonClicked(true);
      saveFaceInfo({
        uuid: faceId,
        name: faceName,
        profile_pic: profilePic || '',
        delete: false,
      });
    } else {
      message.error("未找到用户ID，无法保存");
    }
  };

  // 处理名称双击
  const handleNameDoubleClick = () => {
    setIsEditingName(true);
  };

  // 处理名称输入框失去焦点
  const handleNameInputBlur = () => {
    setIsEditingName(false);

    // 如果修改了名称，则调用保存API
    if (faceId && faceName !== displayName && faceName.trim() !== "") {
      setDisplayName(faceName.trim());
      saveFaceInfo({
        uuid: faceId,
        name: faceName.trim(),
        delete:false,
        profile_pic: profilePic || '',
      });
    }
  };

  // 处理视频报错 - 与App端保持一致，直接删除对应视频
  const handleReportVideo = (videoId: number) => {
    const video = videoData.find((v) => v.id === videoId);
    if (!video || !faceId) return;

    // 使用useRequest处理删除视频
    runDeleteVideo({
      uuid: faceId,
      video: [video.file],
    });
    // 删除成功后会通过runDeleteVideo的onSuccess回调重新获取视频列表
  };

  // 进入编辑模式
  const enterEditMode = () => {
    setIsEditingPhotos(true);
  };

  // 退出编辑模式 - 使用共享函数
  const exitEditMode = () => {
    resetEdit(
      setIsEditingPhotos,
      setSelectedPhotos as (ids: Array<string | number>) => void,
      albumPhotos,
      setAlbumPhotos,
      [], // 我们不使用capturePhotos，传空数组
      setCapturePhotos
    );
  };

  // 处理照片选择 - 使用共享函数
  const handlePhotoSelect = (
    photoId: string | number,
    source: "album" | "capture",
    event: React.MouseEvent
  ) => {
    if (!isEditingPhotos) return;

    // 使用共享函数更新照片选择状态
    togglePhoto(
      photoId,
      albumPhotos,
      setAlbumPhotos,
      selectedPhotos as Array<string | number>,
      setSelectedPhotos as (ids: Array<string | number>) => void
    );
  };

  // 处理删除选中照片
  const handleDeleteSelectedPhotos = () => {
    if (selectedPhotos.length === 0 || !faceId) return;

    // 获取要删除的照片URL列表
    const photosToDelete = albumPhotos
      .filter((photo) => photo.selected && photo.src)
      .map((photo) => photo.src)
      .filter((src): src is string => !!src); // 过滤掉undefined值

    if (photosToDelete.length === 0) {
      message.error("未选择有效照片");
      return;
    }

    // 调用删除照片API
    runDeletePhoto({
      uuid: faceId,
      photo: photosToDelete,
    });
    // API成功回调中会处理刷新照片列表和重置编辑状态
  };

  const lookBackDetail = useCallback((video) => {
    const date = new Date(Number(video?.time));
    const time: string = format(date, "HH:mm");
    setCurData({
      id: video?.id,
      eventTime: time,
      eventName: eventDefinition[video?.event_name]?.label,
      deviceName: "",
      url: video?.cover_file || "",
      movieUrl: video?.file,
    });
    setModalIsShow(true);
  }, []);

  // 渲染名称区域
  const renderNameSection = () => {
    // 判断是否为未标记的陌生人
    const isUnmarked = !faceName || faceName === "未标记";

    if (isEditingName) {
      return (
        <Input
          className={styles.nameInput}
          value={faceName}
          onChange={(e) => setFaceName(e.target.value)}
          onBlur={handleNameInputBlur}
          onPressEnter={handleNameInputBlur}
          autoFocus
          placeholder="请输入备注名称"
        />
      );
    }
    
    if (isUnmarked) {
      // 对未标记的陌生人显示提示文本
      return (
        <div className={`${styles.nameLabel} ${styles.unmarkedName}`} onDoubleClick={handleNameDoubleClick}>
          <span className={styles.addRemarkText}>添加备注</span>
        </div>
      );
    }
    
    return (
      <div className={styles.nameLabel} onDoubleClick={handleNameDoubleClick}>
        {displayName}
      </div>
    );
  };

  // 打开照片选择对话框
  const handleAddPhoto = () => {
    message.info("一期暂不支持");
    // 在实际应用中，这里应该打开照片选择对话框
    // 并在用户选择照片后调用上传API
  };

  // 渲染照片选项卡内容
  const renderPhotoTab = () => {
    if (albumPhotos.length === 0) {
      return (
        <div className={styles.photoTab}>
          <div className={styles.emptyPhotoContainer}>
            <img
              alt=""
              src={isDarkMode ? nullDataDark : nullData}
              className={styles.emptyIcon}
            ></img>
            <div className={styles.emptyText}>暂无照片</div>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.photoTab}>
        <div className={styles.albumSection}>
          <div className={styles.sectionTitle}>
            人脸照片
            {!isEditingPhotos && (
              <span className={styles.editButton} onClick={enterEditMode}>
                <EditOutlined />
              </span>
            )}
          </div>
          <div className={styles.photoGrid}>
            <div className={styles.addPhotoBox} onClick={handleAddPhoto}>
              <div className={styles.plusIcon}></div>
              <div className={styles.addText}>关联人脸</div>
            </div>
            {albumPhotos.map((photo) => (
              <div
                key={photo.id}
                className={`${styles.photoItem} ${
                  photo.selected ? styles.selectedPhoto : ""
                } ${isEditingPhotos ? styles.editingPhoto : ""}`}
                onClick={(e) =>
                  handlePhotoSelect(photo.id as string, "album", e)
                }
              >
                <PreloadImage
                  src={splitURL(photo.src || "")}
                  className={styles.photo}
                  needHeader={true}
                />
                {isEditingPhotos && (
                  <div
                    className={`${styles.selectMark} ${
                      photo.selected ? styles.selected : ""
                    }`}
                  >
                    {photo.selected ? (
                      <CheckOutlined />
                    ) : (
                      <div className={styles.emptyCircle}></div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // 渲染最近视频选项卡内容
  const renderVideoTab = () => {
    if (!hasVideoData) {
      return (
        <div className={styles.videoTab}>
          <div className={styles.emptyVideoContainer}>
            <img
              alt=""
              src={isDarkMode ? nullDataDark : nullData}
              className={styles.emptyIcon}
            ></img>
            <div className={styles.emptyText}>暂无相关视频</div>
          </div>
        </div>
      );
    }

    // 按日期分组视频
    const groupedVideos: Record<string, VideoItem[]> = {};
    videoData.forEach((video) => {
      if (!groupedVideos[video.date]) {
        groupedVideos[video.date] = [];
      }
      groupedVideos[video.date].push(video);
    });

    return (
      <div className={styles.videoTab}>
        {Object.keys(groupedVideos).map((date) => {
          const videos = groupedVideos[date];
          const isToday = videos[0].isToday;

          return (
            <div key={date} className={styles.videoDateGroup}>
              <div className={styles.videoDateHeader}>
                {date} {isToday ? "今天" : ""}
              </div>

              {videos.map((video) => {
                return (
                  <div key={video.id} className={styles.videoItem}>
                    <div className={styles.videoItemLeft}>
                      <PreloadImage
                        style={{ minHeight: "auto", minWidth: "auto" }}
                        src={video.face_file ? splitURL(video.face_file) : ""}
                        alt=""
                        className={styles.videoAvatar}
                        needHeader={true}
                      />
                      <div className={styles.videoInfo}>
                        <div className={styles.videoTime}>
                          {new Date(parseInt(video.time)).toLocaleTimeString()}
                        </div>
                        <div className={styles.videoDesc}>{video.subText}</div>
                      </div>
                    </div>

                    <div className={styles.videoItemRight}>
                      <PreloadImage
                        style={{ minHeight: "auto", minWidth: "auto" }}
                        src={video.cover_file ? splitURL(video.cover_file) : ""}
                        alt=""
                        className={styles.videoThumb}
                        onClick={() => lookBackDetail(video)}
                        needHeader={true}
                      />
                      <div
                        className={styles.reportButton}
                        onClick={() => handleReportVideo(video.id as number)}
                      >
                        报错
                      </div>
                    </div>
                  </div>
                );
              })}

              {date !==
                Object.keys(groupedVideos)[
                  Object.keys(groupedVideos).length - 1
                ] && <div className={styles.videoDateDivider}></div>}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <Modal
        open={visible}
        footer={null}
        width={836}
        centered
        closable={false}
        maskClosable={false}
        className={styles.faceDetailModal}
        destroyOnClose
        onClose={handleClose}
      >
        <div className={styles.modalHeader}>
          <div
            className={styles.closeButton}
            onClick={handleClose}
          >
            <CloseOutlined style={{ color: "var(--text-color)" }} />
          </div>
          <div className={styles.modalTitle}>新建人脸</div>
        </div>

        <div className={styles.modalContent}>
          <div className={styles.leftSection}>
            <div className={styles.avatarContainer}>
              {profilePic ? (
                <PreloadImage
                  src={splitURL(profilePic)}
                  alt="头像"
                  className={styles.avatarPlaceholder}
                  needHeader={true}
                />
              ) : (
                <img
                  src={albumPhotos.length > 0 ? albumPhotos[0].src : avatar}
                  alt="头像"
                  className={styles.avatarPlaceholder}
                  onError={(e) => {
                    // 如果图片加载失败，使用默认头像
                    e.currentTarget.src = avatar;
                  }}
                />
              )}
            </div>
            {renderNameSection()}
          </div>

          <div className={styles.rightSection}>
            <div className={styles.tabsContainer}>
              <div className={styles.tabButtons}>
                {/* <div
                  className={`${styles.tabButton} ${
                    activeTab === "照片" ? styles.active : ""
                  }`}
                  onClick={() => setActiveTab("照片")}
                >
                  照片
                </div> */}
                <div
                  className={`${styles.tabButton} ${
                    activeTab === "最近视频" ? styles.active : ""
                  }`}
                  onClick={() => setActiveTab("最近视频")}
                >
                  最近视频
                </div>
                <div
                  className={`${styles.tabButton}`}
                  style={{ cursor: "not-allowed", pointerEvents: "none" }}
                  onClick={() => setActiveTab("照片")}
                ></div>
              </div>

              <div className={styles.tabContent}>
                {activeTab === "照片" ? renderPhotoTab() : renderVideoTab()}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.actionButtons}>
          <div>
            <Button
              danger
              className={styles.deleteUserButton}
              onClick={handleDeleteMark}
            >
              删除标记
            </Button>
            <Button
              danger
              className={styles.deleteUserButton}
              onClick={handlePermanentDelete}
            >
              彻底删除
            </Button>
          </div>

          {activeTab === "照片" && isEditingPhotos ? (
            <div className={styles.editButtonGroup}>
              <Button className={styles.exitEditButton} onClick={exitEditMode}>
                退出编辑
              </Button>
              <Button
                danger
                className={styles.deletePhotosButton}
                onClick={handleDeleteSelectedPhotos}
                disabled={selectedPhotos.length === 0}
              >
                删除
              </Button>
            </div>
          ) : (
            <Button
              type="primary"
              className={styles.saveButton}
              onClick={handleSave}
            >
              保存
            </Button>
          )}
        </div>

        {/* 删除成功提示 */}
        {deleteToastVisible && (
          <div className={styles.deleteSuccessToast}>删除成功</div>
        )}
      </Modal>

      <LookBackMovieList
        data={eventData}
        curData={curData}
        isShow={modalIsShow}
        setIsShow={setModalIsShow}
      />
    </>
  );
};

export default FaceDetail;
